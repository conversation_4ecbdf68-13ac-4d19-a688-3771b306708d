#!/usr/bin/env python3
"""
Batch EasyOCR Processing Script

Processes all chapters with EasyOCR in batches to avoid memory issues.
"""

import subprocess
import sys
from pathlib import Path
import time


def run_easyocr_on_chapter(chapter_name):
    """Run EasyOCR on a specific chapter."""
    print(f"🔄 Processing {chapter_name}...")
    
    try:
        # Run the EasyOCR extractor
        result = subprocess.run([
            'manga_env/bin/python', 
            'easyocr_manga_extractor.py', 
            '--chapter', 
            chapter_name
        ], capture_output=True, text=True, timeout=600)
        
        if result.returncode == 0:
            print(f"✅ {chapter_name} completed successfully")
            return True
        else:
            print(f"❌ {chapter_name} failed: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"⏰ {chapter_name} timed out")
        return False
    except Exception as e:
        print(f"❌ {chapter_name} error: {e}")
        return False


def get_chapters_to_process():
    """Get list of chapters that need processing."""
    chapters_dir = Path("chapters")
    all_chapters = []
    
    for i in range(1, 51):  # chapters 1-50
        chapter_name = f"chapter_{i}"
        chapter_path = chapters_dir / chapter_name
        
        if chapter_path.exists():
            # Check if EasyOCR transcription already exists
            easyocr_file = chapter_path / f"{chapter_name}_easyocr_transcription.txt"
            if not easyocr_file.exists():
                all_chapters.append(chapter_name)
            else:
                print(f"⏭️  {chapter_name} already processed, skipping")
    
    return all_chapters


def main():
    """Main batch processing function."""
    print("🎌 BATCH EASYOCR PROCESSING")
    print("=" * 50)
    
    # Get chapters that need processing
    chapters_to_process = get_chapters_to_process()
    
    if not chapters_to_process:
        print("🎉 All chapters already processed!")
        return
    
    print(f"📋 Found {len(chapters_to_process)} chapters to process:")
    for chapter in chapters_to_process:
        print(f"   • {chapter}")
    
    print("\n🚀 Starting batch processing...\n")
    
    successful = 0
    failed = 0
    
    for i, chapter in enumerate(chapters_to_process, 1):
        print(f"[{i}/{len(chapters_to_process)}] ", end="")
        
        if run_easyocr_on_chapter(chapter):
            successful += 1
        else:
            failed += 1
        
        # Small delay between chapters to prevent memory issues
        time.sleep(2)
        print()
    
    print("=" * 50)
    print(f"📊 BATCH PROCESSING COMPLETE")
    print(f"✅ Successful: {successful}")
    print(f"❌ Failed: {failed}")
    print(f"📁 Total processed: {successful + failed}")
    
    if failed > 0:
        print(f"\n⚠️  {failed} chapters failed. You can re-run this script to retry failed chapters.")


if __name__ == "__main__":
    main()
