#!/usr/bin/env python3
"""
Check EasyOCR processing progress
"""

from pathlib import Path

def check_progress():
    """Check which chapters have been processed with EasyOCR."""
    chapters_dir = Path("chapters")
    completed = []
    remaining = []
    
    for i in range(1, 51):  # chapters 1-50
        chapter_name = f"chapter_{i}"
        chapter_path = chapters_dir / chapter_name
        
        if chapter_path.exists():
            easyocr_file = chapter_path / f"{chapter_name}_easyocr_transcription.txt"
            if easyocr_file.exists():
                completed.append(chapter_name)
            else:
                remaining.append(chapter_name)
    
    print(f"📊 EASYOCR PROCESSING PROGRESS")
    print(f"=" * 40)
    print(f"✅ Completed: {len(completed)}/50 chapters")
    print(f"⏳ Remaining: {len(remaining)}/50 chapters")
    print()
    
    if completed:
        print(f"✅ Completed chapters:")
        for chapter in completed:
            print(f"   • {chapter}")
        print()
    
    if remaining:
        print(f"⏳ Remaining chapters:")
        for chapter in remaining:
            print(f"   • {chapter}")
    
    return len(completed), len(remaining)

if __name__ == "__main__":
    completed, remaining = check_progress()
    
    if remaining == 0:
        print("\n🎉 ALL CHAPTERS COMPLETED!")
    else:
        print(f"\n🔄 {remaining} chapters still need processing")
