torchvision-0.23.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
torchvision-0.23.0.dist-info/LICENSE,sha256=ZQL2doUc_iX4r3VTHfsyN1tzJbc8N-e0N0H6QiiT5x0,1517
torchvision-0.23.0.dist-info/METADATA,sha256=8-g68Z6uyBlfkg3TYXZlS0VwFUemPtwTk2nVYK5lU7g,6134
torchvision-0.23.0.dist-info/RECORD,,
torchvision-0.23.0.dist-info/WHEEL,sha256=ldzNN3hSb8DQCcLKec5wphe2orfL8_DXXXMF4XinfS8,109
torchvision-0.23.0.dist-info/top_level.txt,sha256=ucJZoaluBW9BGYT4TuCE6zoZY_JuSP30wbDh-IRpxUU,12
torchvision/.dylibs/libc++.1.0.dylib,sha256=U9I82f8VHZli73f5iYcDgGqEEZs5JmQPTAVyTdXx8jQ,1074224
torchvision/.dylibs/libjpeg.8.2.2.dylib,sha256=bWyR3rcwQ4zxER3QSzKC3fYmuE5rsMZ-cRiohYWE5C0,370368
torchvision/.dylibs/libpng16.16.dylib,sha256=hdbq7jrQ1xZUViAVIAg2wIcqU9-S16Ibo6bpySmQ-zE,275376
torchvision/.dylibs/libsharpyuv.0.0.1.dylib,sha256=18fr1usOnujWHpPJw7ukWKG7W-h2WBhJxH0V_o5Kbdo,85920
torchvision/.dylibs/libwebp.7.1.8.dylib,sha256=K1PMdyb9uwoQV771kYhpdunqppzMpqyvWPNflJL-vMA,512800
torchvision/.dylibs/libz.1.2.13.dylib,sha256=Yp_BvBAwulvLScfDRa5rmRlmGw1KI09PNU1_lH7t-Zk,157696
torchvision/_C.so,sha256=-XcK7_F_Huv3auOYF__RdgNk1WgYQCZZj8S-dSyMztI,1504416
torchvision/__init__.py,sha256=7iyfQRDPEgPbSMQmAWBzKawfGXCfqRwVL42V61NDenM,3534
torchvision/__pycache__/__init__.cpython-313.pyc,,
torchvision/__pycache__/_internally_replaced_utils.cpython-313.pyc,,
torchvision/__pycache__/_meta_registrations.cpython-313.pyc,,
torchvision/__pycache__/_utils.cpython-313.pyc,,
torchvision/__pycache__/extension.cpython-313.pyc,,
torchvision/__pycache__/utils.cpython-313.pyc,,
torchvision/__pycache__/version.cpython-313.pyc,,
torchvision/_internally_replaced_utils.py,sha256=SmFV-P4ETuJBIbrFUV4s1rXSpP_FI7U72CPO6q-6mwA,1407
torchvision/_meta_registrations.py,sha256=lkEGW61fKUrGSh0iOFsZ1ZHskItS1EJ9Oo2UfM-OvQ8,7208
torchvision/_utils.py,sha256=n8sk2bv_kzGSpJeLn7_R_vP1a9ob-t3QF8y8-XgZVa8,955
torchvision/datasets/__init__.py,sha256=CQfKDHOvjSZ2dBlOLRieEjP9AsB1jJEe3-5vbFNeXOM,3606
torchvision/datasets/__pycache__/__init__.cpython-313.pyc,,
torchvision/datasets/__pycache__/_optical_flow.cpython-313.pyc,,
torchvision/datasets/__pycache__/_stereo_matching.cpython-313.pyc,,
torchvision/datasets/__pycache__/caltech.cpython-313.pyc,,
torchvision/datasets/__pycache__/celeba.cpython-313.pyc,,
torchvision/datasets/__pycache__/cifar.cpython-313.pyc,,
torchvision/datasets/__pycache__/cityscapes.cpython-313.pyc,,
torchvision/datasets/__pycache__/clevr.cpython-313.pyc,,
torchvision/datasets/__pycache__/coco.cpython-313.pyc,,
torchvision/datasets/__pycache__/country211.cpython-313.pyc,,
torchvision/datasets/__pycache__/dtd.cpython-313.pyc,,
torchvision/datasets/__pycache__/eurosat.cpython-313.pyc,,
torchvision/datasets/__pycache__/fakedata.cpython-313.pyc,,
torchvision/datasets/__pycache__/fer2013.cpython-313.pyc,,
torchvision/datasets/__pycache__/fgvc_aircraft.cpython-313.pyc,,
torchvision/datasets/__pycache__/flickr.cpython-313.pyc,,
torchvision/datasets/__pycache__/flowers102.cpython-313.pyc,,
torchvision/datasets/__pycache__/folder.cpython-313.pyc,,
torchvision/datasets/__pycache__/food101.cpython-313.pyc,,
torchvision/datasets/__pycache__/gtsrb.cpython-313.pyc,,
torchvision/datasets/__pycache__/hmdb51.cpython-313.pyc,,
torchvision/datasets/__pycache__/imagenet.cpython-313.pyc,,
torchvision/datasets/__pycache__/imagenette.cpython-313.pyc,,
torchvision/datasets/__pycache__/inaturalist.cpython-313.pyc,,
torchvision/datasets/__pycache__/kinetics.cpython-313.pyc,,
torchvision/datasets/__pycache__/kitti.cpython-313.pyc,,
torchvision/datasets/__pycache__/lfw.cpython-313.pyc,,
torchvision/datasets/__pycache__/lsun.cpython-313.pyc,,
torchvision/datasets/__pycache__/mnist.cpython-313.pyc,,
torchvision/datasets/__pycache__/moving_mnist.cpython-313.pyc,,
torchvision/datasets/__pycache__/omniglot.cpython-313.pyc,,
torchvision/datasets/__pycache__/oxford_iiit_pet.cpython-313.pyc,,
torchvision/datasets/__pycache__/pcam.cpython-313.pyc,,
torchvision/datasets/__pycache__/phototour.cpython-313.pyc,,
torchvision/datasets/__pycache__/places365.cpython-313.pyc,,
torchvision/datasets/__pycache__/rendered_sst2.cpython-313.pyc,,
torchvision/datasets/__pycache__/sbd.cpython-313.pyc,,
torchvision/datasets/__pycache__/sbu.cpython-313.pyc,,
torchvision/datasets/__pycache__/semeion.cpython-313.pyc,,
torchvision/datasets/__pycache__/stanford_cars.cpython-313.pyc,,
torchvision/datasets/__pycache__/stl10.cpython-313.pyc,,
torchvision/datasets/__pycache__/sun397.cpython-313.pyc,,
torchvision/datasets/__pycache__/svhn.cpython-313.pyc,,
torchvision/datasets/__pycache__/ucf101.cpython-313.pyc,,
torchvision/datasets/__pycache__/usps.cpython-313.pyc,,
torchvision/datasets/__pycache__/utils.cpython-313.pyc,,
torchvision/datasets/__pycache__/video_utils.cpython-313.pyc,,
torchvision/datasets/__pycache__/vision.cpython-313.pyc,,
torchvision/datasets/__pycache__/voc.cpython-313.pyc,,
torchvision/datasets/__pycache__/widerface.cpython-313.pyc,,
torchvision/datasets/_optical_flow.py,sha256=uNrQMLxd5PXpdvC3Lr2BYUcwMHUzw561AHScEaEy5QE,21200
torchvision/datasets/_stereo_matching.py,sha256=gS4Nhz-1Gbm975ANFVxkNjpeFK01081Jchl045rbXEQ,49040
torchvision/datasets/caltech.py,sha256=zdWY0yWpQTqdza7FhrAQ3r-mPgXf-cVvJRFgwM8AShE,8798
torchvision/datasets/celeba.py,sha256=lrqn1ik8an81nSBqgirU82Lgh-s2LhnBuf45c3MAVRo,8542
torchvision/datasets/cifar.py,sha256=Ci1nhujp0em8fbBmTQGFvBT7IsZzPo7m3edQA4tu2DU,5784
torchvision/datasets/cityscapes.py,sha256=KD516SRjAjlfdsQ57wPRo76kl8RrMbrQEoTUhNdJJHw,10330
torchvision/datasets/clevr.py,sha256=YdWTXg2y4ubW3kFyS6RaNgGo-oPWNC37hPjbzMJFLQY,3855
torchvision/datasets/coco.py,sha256=2N0zCOkhEbuLWPaQjMkJe28nxvO18O65je692Xgv0TU,4345
torchvision/datasets/country211.py,sha256=-HSUiid5jC7ooNzZ0hYnv2__DmzSp_aQIwW9yvBJ4W4,2889
torchvision/datasets/dtd.py,sha256=J3jMDI-0yUE9G2jBy7CukPXF-5baOhxdYUXLaUCTuxw,4420
torchvision/datasets/eurosat.py,sha256=C8Q3a4vF-hSYM-xEfVglewgOJVek6DWBYarGkMB90RQ,2761
torchvision/datasets/fakedata.py,sha256=b9fUKMRmnwv_aF_k99wSEB953bQtb9NTz9pTk354Eg0,2440
torchvision/datasets/fer2013.py,sha256=8qRPt6CU7JZ_SqScXLF3tnxWsLPlx57EC8Qe5BQJgtc,5106
torchvision/datasets/fgvc_aircraft.py,sha256=K6ik7tWzUSRUP2Ed0cIzUH-bozMl8i2_Xh0_2IK97hU,4968
torchvision/datasets/flickr.py,sha256=rVS8l-xNTEl8r-vFaAnJJyT8T5jHorMXUZaj83xlK7g,6170
torchvision/datasets/flowers102.py,sha256=NELj-lyuVsvMuin5YVOdZ5Ih-DEErTPZILOrEPrB4RU,7481
torchvision/datasets/folder.py,sha256=sqNuUgz0UdgrmT5v2qEmbYUm72pALc9AHyPwyc0Kjvo,12985
torchvision/datasets/food101.py,sha256=8_1Duos86AEelNt2J5I2vbmFA18P4FiL8JICNnNINHg,4145
torchvision/datasets/gtsrb.py,sha256=zdBkamw8DcfRq8IVIt3tEhSooH53NhI0K-Ojvj3AUVM,3778
torchvision/datasets/hmdb51.py,sha256=XNSgjA2p4UrQ67-mUR8JgsLvgKAtmwjiR4SfTu5JBW0,5952
torchvision/datasets/imagenet.py,sha256=r6AzyjPv5TkwxcnXomQZyu4cE0oIiHYdVBXkFX8E_y0,8922
torchvision/datasets/imagenette.py,sha256=ZN6igkIUReoX01N0LR2wLpAEE3cBeFEASV1NNUkvCNk,4626
torchvision/datasets/inaturalist.py,sha256=WGnsEaOXyUzgRX5rIpWFHoUufCFhN-nrYu6glQQiOWI,10302
torchvision/datasets/kinetics.py,sha256=6rMjRvSRckN1I8oSZGRp1Vje-IKXrX6PYWuHer4iXk4,9865
torchvision/datasets/kitti.py,sha256=6Ntz3XiowAF-iKpjqNK484cQ5WvaseVqt1do97LfqYM,5624
torchvision/datasets/lfw.py,sha256=gRVXQuPOoZ9WmMuuDEpkY9k09t1aP9D0anhDb4fKKRI,11403
torchvision/datasets/lsun.py,sha256=Cbo_wF_dRo0mgf_4Ua2vvnD1rSunfd5aKOFNZfSwC3c,5730
torchvision/datasets/mnist.py,sha256=zUQHFOW8CjkWTfYuTgJcUVd1fwCDUIH6ciVKRDuBxMU,21804
torchvision/datasets/moving_mnist.py,sha256=D7zBXNXU0-w5lZCDGgT1QaTqBVbiegnGeZQYht_ipK4,3644
torchvision/datasets/omniglot.py,sha256=HPgmjk9bvV2A8DQnyfWP_VUiQFHl8mzQlO5SB8fw49E,4486
torchvision/datasets/oxford_iiit_pet.py,sha256=cEL3fuVq8Z9jxcDe-_wk18zVO1rhuSaKhl144ue25Uo,5696
torchvision/datasets/pcam.py,sha256=eFhow5mBwvyRML6JVX5uJ6vE7yy9fcutz1BiMm-GE4A,5278
torchvision/datasets/phototour.py,sha256=r3IahNs_KDU4RaPBH5-mm29TBO9VxhOcWSkJUJNHxdc,7855
torchvision/datasets/places365.py,sha256=r7riK2-LsAmx90QptOEQ0_37r9iKymSpi5UKZ7hJPOk,7467
torchvision/datasets/rendered_sst2.py,sha256=3346Z3irlBVjxoXUrQTMctulwFpBY2aL6UaoSmJ8hYk,3957
torchvision/datasets/samplers/__init__.py,sha256=W1ZtQpGLG6aoHylo1t8PEsHIVoWwso5bSFk9JzKfH8g,161
torchvision/datasets/samplers/__pycache__/__init__.cpython-313.pyc,,
torchvision/datasets/samplers/__pycache__/clip_sampler.cpython-313.pyc,,
torchvision/datasets/samplers/clip_sampler.py,sha256=ymUW71YTOrdKs-LQYrY-v85n2SBVbGUnxI1wh1uJros,6265
torchvision/datasets/sbd.py,sha256=Lh4KCZGVAbOh1F5ue30XhuZy2bugZNjUv_sDV1BZtwA,5407
torchvision/datasets/sbu.py,sha256=Pxu-XE_oEelysZD2qXf2ek2fNH4fQEd48f_JM76YQqE,4464
torchvision/datasets/semeion.py,sha256=oPd5XVRcUBWl84yokyRHe81mXubY_nAG0kd6NdYYVx0,3099
torchvision/datasets/stanford_cars.py,sha256=eXcOmsErj8ZCuzRHLTy0ZNCAfy-a7NyTTbJ7pUAfWm8,4281
torchvision/datasets/stl10.py,sha256=WLtx6AnSvikS-wJAQhBkz3V1vKXwrJLWXRqmVYKgyXM,7227
torchvision/datasets/sun397.py,sha256=2wdKxeamU-3BCJWF2gLqxQHZVUap1O4HHrbMH2Rn7VI,3176
torchvision/datasets/svhn.py,sha256=CeKi4rQh8Mzd3Ohs-qjUQ5MSFLLSECJB_ktHdbICDUI,4821
torchvision/datasets/ucf101.py,sha256=C4eyBIpDzlCKXtzC2XFntBpXxkL4yXkTtTVHIAccUZI,5514
torchvision/datasets/usps.py,sha256=TGQvxv1Yp0qtiqMR8bY13BxPVtIWC_bW9y2LNAxU1J4,3509
torchvision/datasets/utils.py,sha256=Ndj0Ai0xOnc512nx-Sv3VRF144LVC7wzLKfYi2UqnXI,15914
torchvision/datasets/video_utils.py,sha256=0SQKwpLScpvaAbaevySYt9gdfn82C5AO0Li0k-VnEW8,17194
torchvision/datasets/vision.py,sha256=c6mkDs5_4zvl99DhL75QamDELbUVsEl3bYA36wfm0Gs,4236
torchvision/datasets/voc.py,sha256=Rf6suZdHFHDvtYZfKSmbxc-chJzCGB52tP_ltjrL-uA,8816
torchvision/datasets/widerface.py,sha256=plgSlVHybKCvZRLDQRtykU9kY1-lSlSGP8ppW565fWo,8241
torchvision/extension.py,sha256=YWBDURfCFXSmRvXi2iEg2L0hafN2-RnybpImh9JAUtQ,3141
torchvision/image.so,sha256=f1t4xtcI11ld8jglQ24ErRFvQ0NqRsDn0bGKAt6OFyY,251088
torchvision/io/__init__.py,sha256=-6ONNvXi97z0x3UjxLBwstOTG8KcJg_sFGha3oGFjAo,1583
torchvision/io/__pycache__/__init__.cpython-313.pyc,,
torchvision/io/__pycache__/_load_gpu_decoder.cpython-313.pyc,,
torchvision/io/__pycache__/_video_deprecation_warning.cpython-313.pyc,,
torchvision/io/__pycache__/_video_opt.cpython-313.pyc,,
torchvision/io/__pycache__/image.cpython-313.pyc,,
torchvision/io/__pycache__/video.cpython-313.pyc,,
torchvision/io/__pycache__/video_reader.cpython-313.pyc,,
torchvision/io/_load_gpu_decoder.py,sha256=Cc8eP620qPDFc0q2qd-VYtjxtsgFPjOgg7Z04RXRziU,178
torchvision/io/_video_deprecation_warning.py,sha256=fP51Uk1VKpvV2j_dPPIRoFJLqWd7V2Dg9oRCBZM7rbY,450
torchvision/io/_video_opt.py,sha256=jieVjsX45GlUQTnuNKvtgb2hj7OjXB_lqDE1xZZ4lTo,20783
torchvision/io/image.py,sha256=D8CD1fofY4Lnwmjpd_vNJLTAWHBr6EP_KRdq0-SPJks,21712
torchvision/io/video.py,sha256=Qv-hAIVVIyI7bLRFmH03_yf6lTdJvdGgyEkuP-djmA4,18338
torchvision/io/video_reader.py,sha256=uI8zWQf8cR_7O0fkDEyasPgLvcaZKg1jaOtVVPVqglw,11862
torchvision/models/__init__.py,sha256=A8GQPE1bl3oUHpuD9ND53DV557IPY4459FNLW6sVXGI,865
torchvision/models/__pycache__/__init__.cpython-313.pyc,,
torchvision/models/__pycache__/_api.cpython-313.pyc,,
torchvision/models/__pycache__/_meta.cpython-313.pyc,,
torchvision/models/__pycache__/_utils.cpython-313.pyc,,
torchvision/models/__pycache__/alexnet.cpython-313.pyc,,
torchvision/models/__pycache__/convnext.cpython-313.pyc,,
torchvision/models/__pycache__/densenet.cpython-313.pyc,,
torchvision/models/__pycache__/efficientnet.cpython-313.pyc,,
torchvision/models/__pycache__/feature_extraction.cpython-313.pyc,,
torchvision/models/__pycache__/googlenet.cpython-313.pyc,,
torchvision/models/__pycache__/inception.cpython-313.pyc,,
torchvision/models/__pycache__/maxvit.cpython-313.pyc,,
torchvision/models/__pycache__/mnasnet.cpython-313.pyc,,
torchvision/models/__pycache__/mobilenet.cpython-313.pyc,,
torchvision/models/__pycache__/mobilenetv2.cpython-313.pyc,,
torchvision/models/__pycache__/mobilenetv3.cpython-313.pyc,,
torchvision/models/__pycache__/regnet.cpython-313.pyc,,
torchvision/models/__pycache__/resnet.cpython-313.pyc,,
torchvision/models/__pycache__/shufflenetv2.cpython-313.pyc,,
torchvision/models/__pycache__/squeezenet.cpython-313.pyc,,
torchvision/models/__pycache__/swin_transformer.cpython-313.pyc,,
torchvision/models/__pycache__/vgg.cpython-313.pyc,,
torchvision/models/__pycache__/vision_transformer.cpython-313.pyc,,
torchvision/models/_api.py,sha256=6UNCE63SIivpOBzkd9JJGW2iRcySzADRr7oP5NDY0As,9976
torchvision/models/_meta.py,sha256=fqpeQBsf9EEYbmApQ8Q0LKyM9_UFwjireII5mwDbwJY,28875
torchvision/models/_utils.py,sha256=sA_0VGDqB_oI44SVJZ9ZVN_SBQWH60yTmND2lF3197g,10880
torchvision/models/alexnet.py,sha256=dvBZLVH60TOTHCNNkWg0TFLtuJ5Ghh_xXN73r3Vyq58,4488
torchvision/models/convnext.py,sha256=XADEErx55cZQ-Iff4NIaqAKJA6O76uGpXDsP372vVcI,15347
torchvision/models/densenet.py,sha256=dqcXl7eHMIjCfKBFhYiGHBl1KOhkIfV1JdyOOtdxxZo,16812
torchvision/models/detection/__init__.py,sha256=JwYm_fTGO_FeRg4eTOQLwQPZ9lC9jheZ-QEoJgqKTjg,168
torchvision/models/detection/__pycache__/__init__.cpython-313.pyc,,
torchvision/models/detection/__pycache__/_utils.cpython-313.pyc,,
torchvision/models/detection/__pycache__/anchor_utils.cpython-313.pyc,,
torchvision/models/detection/__pycache__/backbone_utils.cpython-313.pyc,,
torchvision/models/detection/__pycache__/faster_rcnn.cpython-313.pyc,,
torchvision/models/detection/__pycache__/fcos.cpython-313.pyc,,
torchvision/models/detection/__pycache__/generalized_rcnn.cpython-313.pyc,,
torchvision/models/detection/__pycache__/image_list.cpython-313.pyc,,
torchvision/models/detection/__pycache__/keypoint_rcnn.cpython-313.pyc,,
torchvision/models/detection/__pycache__/mask_rcnn.cpython-313.pyc,,
torchvision/models/detection/__pycache__/retinanet.cpython-313.pyc,,
torchvision/models/detection/__pycache__/roi_heads.cpython-313.pyc,,
torchvision/models/detection/__pycache__/rpn.cpython-313.pyc,,
torchvision/models/detection/__pycache__/ssd.cpython-313.pyc,,
torchvision/models/detection/__pycache__/ssdlite.cpython-313.pyc,,
torchvision/models/detection/__pycache__/transform.cpython-313.pyc,,
torchvision/models/detection/_utils.py,sha256=atlD5LtlCQTI-NNgoWWHUyBKe8qZS45P2ZU-8LECkyU,22107
torchvision/models/detection/anchor_utils.py,sha256=EzkBmk7iTcCSOAxg2I9ss71ijAvOCzlmyC140KD5Hig,11853
torchvision/models/detection/backbone_utils.py,sha256=Iyg9x1l2dkWbKbEwF31OJ9Q834s13M1oZmzN_sP0250,10536
torchvision/models/detection/faster_rcnn.py,sha256=S6eTCrHkWICDpGIgjAAjLcwgasEub8WYUvpIxmjysms,36966
torchvision/models/detection/fcos.py,sha256=sxbbHqDdCEjsgo-oIChwcZesj0u3RzrSh-uij-tpPig,34216
torchvision/models/detection/generalized_rcnn.py,sha256=ppKLsSiDnLmZXo9fjmTbR8rM7My-V2HN3JTmAEkmqHQ,4929
torchvision/models/detection/image_list.py,sha256=MZpxox8L4yXk3gdSADP__Xq40OcN75p0dlG2hGIb2EE,751
torchvision/models/detection/keypoint_rcnn.py,sha256=vzaZlzWlVml7qyXsTxy4tuMsWBeLhugLoEDDcvudAi0,21979
torchvision/models/detection/mask_rcnn.py,sha256=KK_0MLEZP9GWfCDEl6mJuYBHidWodGYfPxiyRmKWhNE,26713
torchvision/models/detection/retinanet.py,sha256=q-JYhDaweNTpW0HO3d7wAyWRrzQ-S9zafv9UquAM6Bc,37281
torchvision/models/detection/roi_heads.py,sha256=Wh051KNju71PeY7edl85tbHXPlmvg9noxQ44Gpq5dOw,33821
torchvision/models/detection/rpn.py,sha256=F-uTkiSyjENYObeVjGqyV2XwQcFRvhzxN1B90OTV3dU,15818
torchvision/models/detection/ssd.py,sha256=mXkTRD0XQuVJJ5FAt_ODNcLTyxhX-OeDZ6IxdmBjiXk,28960
torchvision/models/detection/ssdlite.py,sha256=9p8NaIPICi2A-dbD4oa1yZyKO30x5USGz2XNb_l09Zc,13207
torchvision/models/detection/transform.py,sha256=j1MUnNk8O8KRzwgVNWhighN64psjYpOOcynyNhMlBh8,12170
torchvision/models/efficientnet.py,sha256=gtBY4Zie-E5O985D5KSdwQ8bldzQAzA8LAUkON6COxc,43098
torchvision/models/feature_extraction.py,sha256=7P4xGde3MQt6rvdZUZMwfCk93DZABcEQY7sa7CDQXVw,27914
torchvision/models/googlenet.py,sha256=zVghxRPGRApc3Lj4l_AzEj16EWR2BcNT7WC-E2Q60lM,12793
torchvision/models/inception.py,sha256=Z5Hbfi7ot6KTWay20g2eW0dEGLpGwN26W8wV0lO4rYU,18838
torchvision/models/maxvit.py,sha256=c6vFrELdAhRG1XOrXowTJJp25Fifdqu6TeKgITO4PqY,32110
torchvision/models/mnasnet.py,sha256=JPZR-yvXDtZ_Mve2ZPJedBHTEI_bkwgsWHESK-fuA-A,17562
torchvision/models/mobilenet.py,sha256=lSRVxw2TL3LFBwCadvyvH6n3GzqUTnK2-rhX3MOgSrs,211
torchvision/models/mobilenetv2.py,sha256=fYp06-r4Di7PxqSMLQeSHiaM1d2XtZlNul73AbRtOyQ,9704
torchvision/models/mobilenetv3.py,sha256=VESQk2JzSrVPB7pkzS1U6QhUll1NHOayszfgVdD8uz4,16300
torchvision/models/optical_flow/__init__.py,sha256=0zRlMWQJCjFqoUafUXVgO89-z7em7tACo9E8hHSq9RQ,20
torchvision/models/optical_flow/__pycache__/__init__.cpython-313.pyc,,
torchvision/models/optical_flow/__pycache__/_utils.cpython-313.pyc,,
torchvision/models/optical_flow/__pycache__/raft.cpython-313.pyc,,
torchvision/models/optical_flow/_utils.py,sha256=v-tQJzYmYukrD1sQAE-5j5jxyvComwF1UdGkz5tVTLw,2077
torchvision/models/optical_flow/raft.py,sha256=HYnzlhbjuOifVsvcgT5nB3k6URazMXRs-AsEUOnozTg,39991
torchvision/models/quantization/__init__.py,sha256=gqFM7zI4UUHKKBDJAumozOn7xPL0JtvyNS8Ejz6QXp0,125
torchvision/models/quantization/__pycache__/__init__.cpython-313.pyc,,
torchvision/models/quantization/__pycache__/googlenet.cpython-313.pyc,,
torchvision/models/quantization/__pycache__/inception.cpython-313.pyc,,
torchvision/models/quantization/__pycache__/mobilenet.cpython-313.pyc,,
torchvision/models/quantization/__pycache__/mobilenetv2.cpython-313.pyc,,
torchvision/models/quantization/__pycache__/mobilenetv3.cpython-313.pyc,,
torchvision/models/quantization/__pycache__/resnet.cpython-313.pyc,,
torchvision/models/quantization/__pycache__/shufflenetv2.cpython-313.pyc,,
torchvision/models/quantization/__pycache__/utils.cpython-313.pyc,,
torchvision/models/quantization/googlenet.py,sha256=8WXqjmlVw0sVy4vUkQvoAA1YXOGE3k_tVTkg36SeI4A,8112
torchvision/models/quantization/inception.py,sha256=i5bUGl4G59SlNOjxffHlJqDikeesgim31GwGHyubMwI,10841
torchvision/models/quantization/mobilenet.py,sha256=lSRVxw2TL3LFBwCadvyvH6n3GzqUTnK2-rhX3MOgSrs,211
torchvision/models/quantization/mobilenetv2.py,sha256=SP8uiSC9vwKtIkgzxRt4Avn8HWmfBbHLDB21BkkyuCI,5915
torchvision/models/quantization/mobilenetv3.py,sha256=BshvE-qRjdDbY0eYep-8T2L3BNHd42dhxFD4QOn3v94,9256
torchvision/models/quantization/resnet.py,sha256=PBKMdDxaIwEDXmnyIbUvyhG-kt-gmrWOus8TQnfs_TA,18055
torchvision/models/quantization/shufflenetv2.py,sha256=__r5U8MsNExD-mgyC6moYItAfdaRPt4kr1LtwC-Kwa0,17006
torchvision/models/quantization/utils.py,sha256=1fsjPdXM0AxcI7LRYWNyLOk52Jte56ekwLMJL9osF08,2052
torchvision/models/regnet.py,sha256=mHxtyaiGH6n4kfp8weha09VuO6ySMMLGdtV7w0ikl6I,63534
torchvision/models/resnet.py,sha256=n68vMh_a9-29eRrtxuNhDH7fnIkhEsvXWGG-rh6VGNE,38920
torchvision/models/segmentation/__init__.py,sha256=TGk6UdVXAMtwBpYalrvdXZnmSwqzTDOT1lgKrfzhHrQ,66
torchvision/models/segmentation/__pycache__/__init__.cpython-313.pyc,,
torchvision/models/segmentation/__pycache__/_utils.cpython-313.pyc,,
torchvision/models/segmentation/__pycache__/deeplabv3.cpython-313.pyc,,
torchvision/models/segmentation/__pycache__/fcn.cpython-313.pyc,,
torchvision/models/segmentation/__pycache__/lraspp.cpython-313.pyc,,
torchvision/models/segmentation/_utils.py,sha256=dBx3elgggqel7f3qhPvid_ZDBWcC6DcJd601S-ULaz4,1191
torchvision/models/segmentation/deeplabv3.py,sha256=W93D1mmQuwVeQ6_W7TZn8CEUI5D0GKZ_2auRKhBiB-Q,15042
torchvision/models/segmentation/fcn.py,sha256=I1FqaZZVPc3Fbg_7E2L5qpumnupxBYc7KYsW03EG_Cs,8973
torchvision/models/segmentation/lraspp.py,sha256=oVGMcmR6DvG-M8G33xw2QoAFSy_JfHkHEn2HlNlbvvU,7637
torchvision/models/shufflenetv2.py,sha256=LIe_Ip6jCzbYbQ4zW4zYwWuxJN8qTJobH8SPsMLvydQ,15438
torchvision/models/squeezenet.py,sha256=apjFPEI5nr_493bAQsR245EorzaMYXVQSqdcveyAfy0,8763
torchvision/models/swin_transformer.py,sha256=nsc_IzMQg4nhUv2jRrbxlevO0C6c7TZ872D0BQk7QLE,39331
torchvision/models/vgg.py,sha256=IUpaQh1-VpMxW7kcUIe0dDFmDznJBcl6gGzFSEDfsE8,19213
torchvision/models/video/__init__.py,sha256=O4HB-RaXgCtnvpMDAuMBaIeKIiYEkNxra_fmAHLUIJM,93
torchvision/models/video/__pycache__/__init__.cpython-313.pyc,,
torchvision/models/video/__pycache__/mvit.cpython-313.pyc,,
torchvision/models/video/__pycache__/resnet.cpython-313.pyc,,
torchvision/models/video/__pycache__/s3d.cpython-313.pyc,,
torchvision/models/video/__pycache__/swin_transformer.cpython-313.pyc,,
torchvision/models/video/mvit.py,sha256=dspGXyvgu3_5FMpDQmTOtnNkAoVtFk5YEEkdW6d9QhA,33006
torchvision/models/video/resnet.py,sha256=JvwGBIWc-FN2fPKHFrHN5zu7m3_eomDJdD4MBZqzC-w,16779
torchvision/models/video/s3d.py,sha256=jx9gMP18Bzb7UO3vjejVBHlrCrJPdWFDfTn7XeU5kMg,7815
torchvision/models/video/swin_transformer.py,sha256=ZVO7jXANfWM2wOAasFC1C0jL4mWh9Msyi6iGUNbD2uQ,27675
torchvision/models/vision_transformer.py,sha256=C8WUucp73YSotGMdSt_u6emkCTd24dC0L_d3Nv7a77Y,32124
torchvision/ops/__init__.py,sha256=eVv16QSBwgKaojOUHMPCy4ou9ZeFh-HoCV4DpqrZG4U,1928
torchvision/ops/__pycache__/__init__.cpython-313.pyc,,
torchvision/ops/__pycache__/_box_convert.cpython-313.pyc,,
torchvision/ops/__pycache__/_register_onnx_ops.cpython-313.pyc,,
torchvision/ops/__pycache__/_utils.cpython-313.pyc,,
torchvision/ops/__pycache__/boxes.cpython-313.pyc,,
torchvision/ops/__pycache__/ciou_loss.cpython-313.pyc,,
torchvision/ops/__pycache__/deform_conv.cpython-313.pyc,,
torchvision/ops/__pycache__/diou_loss.cpython-313.pyc,,
torchvision/ops/__pycache__/drop_block.cpython-313.pyc,,
torchvision/ops/__pycache__/feature_pyramid_network.cpython-313.pyc,,
torchvision/ops/__pycache__/focal_loss.cpython-313.pyc,,
torchvision/ops/__pycache__/giou_loss.cpython-313.pyc,,
torchvision/ops/__pycache__/misc.cpython-313.pyc,,
torchvision/ops/__pycache__/poolers.cpython-313.pyc,,
torchvision/ops/__pycache__/ps_roi_align.cpython-313.pyc,,
torchvision/ops/__pycache__/ps_roi_pool.cpython-313.pyc,,
torchvision/ops/__pycache__/roi_align.cpython-313.pyc,,
torchvision/ops/__pycache__/roi_pool.cpython-313.pyc,,
torchvision/ops/__pycache__/stochastic_depth.cpython-313.pyc,,
torchvision/ops/_box_convert.py,sha256=QodQcplX74dw9SuP21MiKAOewEipka9uYrO5XSKipGc,6981
torchvision/ops/_register_onnx_ops.py,sha256=Fyb1kC2m2OqZdfW_M86pt9-S66e1qNUhXNu1EQRa034,4181
torchvision/ops/_utils.py,sha256=iEpmZ1T1B7KkQvaVOt0tavLAfGbfgU0Y6LvglyfWJK4,3617
torchvision/ops/boxes.py,sha256=0_NeU71pzyd1p5hClCwHpDnaDb2ArWWLaJkNHoLlxeI,18305
torchvision/ops/ciou_loss.py,sha256=9Jozu0XjAUdnMNjLu1kjjqJGykNpd7ljGL951pG9EQ4,2755
torchvision/ops/deform_conv.py,sha256=UXyqaidnD_OFssjpBvG4as5-Fj9r-wInnvA1OZYs56s,6983
torchvision/ops/diou_loss.py,sha256=Wh9ZjOyDgcBnRk8Htdm6CqaPDz-7T1_jf64n1ksCI7A,3335
torchvision/ops/drop_block.py,sha256=A4EGIl7txrU_QmkI1N0W9hfd8tq8yx6zq32oYXaddLQ,5855
torchvision/ops/feature_pyramid_network.py,sha256=6CJ_rt_DstnhOu-U34-SLYatqcNie0WLhJDIJCIk2C0,8683
torchvision/ops/focal_loss.py,sha256=mWb89_hjWpOAyCK5rykw0KmOjm_0sQ4ukfp5ivhlBCc,2419
torchvision/ops/giou_loss.py,sha256=eyMV9s0gDqEAFowurcFaR-eHSeT3qP7B6_sHHF8eWjQ,2695
torchvision/ops/misc.py,sha256=orsfOvHTuFR2HqS8NdC53rXkl3Hw3JGbOQZD8IGhq34,13586
torchvision/ops/poolers.py,sha256=Y1cjDl42SXrkajAWHr_WXNLc-8OOEpz4KuGQn0S7ReE,11901
torchvision/ops/ps_roi_align.py,sha256=4iAbeUVTessAcxvJhuARN_aFGUTZC9R4KrKC_mBH3MQ,3625
torchvision/ops/ps_roi_pool.py,sha256=jOv-2pAZdLFvvt4r4NwiRfxU5WAOy_vi6gxZjMvlusw,2870
torchvision/ops/roi_align.py,sha256=6CUHi9CiiMufuPa8-e4tR_BSVbCxSnMAPIeMlMSQLek,11314
torchvision/ops/roi_pool.py,sha256=5TUv79epZ2ME8mg1_nYSJTAGT-iASEbLd5mKCFyqHp8,2937
torchvision/ops/stochastic_depth.py,sha256=ISZ9noJyZLxpTG-wa2VmPs66qjhVsP7ZxWHvumWSP3U,2236
torchvision/transforms/__init__.py,sha256=EMft42B1JAiU11J1rxIN4Znis6EJPbp-bsGjAzH-24M,53
torchvision/transforms/__pycache__/__init__.cpython-313.pyc,,
torchvision/transforms/__pycache__/_functional_pil.cpython-313.pyc,,
torchvision/transforms/__pycache__/_functional_tensor.cpython-313.pyc,,
torchvision/transforms/__pycache__/_functional_video.cpython-313.pyc,,
torchvision/transforms/__pycache__/_presets.cpython-313.pyc,,
torchvision/transforms/__pycache__/_transforms_video.cpython-313.pyc,,
torchvision/transforms/__pycache__/autoaugment.cpython-313.pyc,,
torchvision/transforms/__pycache__/functional.cpython-313.pyc,,
torchvision/transforms/__pycache__/transforms.cpython-313.pyc,,
torchvision/transforms/_functional_pil.py,sha256=owJiMj7vqTUVQFFbH_ePIY7P8s4_btlXsnGUjMjJonY,12118
torchvision/transforms/_functional_tensor.py,sha256=9OCKP9dy1JUkBeqyj5RECjCy0n4nFagw_8h6S2fjE18,33926
torchvision/transforms/_functional_video.py,sha256=YcV557YglbJsq9SRGJHFoRbtxawiLSJ1oM5rV75OyqQ,3857
torchvision/transforms/_presets.py,sha256=Xlp7gLLnPQCjp8eY7w9uRKuQKV6wLs71ZZMIvCghlnA,8504
torchvision/transforms/_transforms_video.py,sha256=Buz5LCWVPGiEonHE-cXIXfbkBhNc0qxVraxkNdxKp8o,4950
torchvision/transforms/autoaugment.py,sha256=8nIciivWfeYKXSl7Z9E2mIKEtBsEAiR5S0M5l511MkQ,28224
torchvision/transforms/functional.py,sha256=Cy_fjmdk9L4BE6zZW1jTsUFYwT-iN07ZP7vh9LhzDe0,67861
torchvision/transforms/transforms.py,sha256=sNl0L2QUC09Aoj40PvKHNMMvD8OLxNFTQDvJ1qxVNAI,85557
torchvision/transforms/v2/__init__.py,sha256=wEzjK3wEbnkp4ygKqdOYNfgMFW-gjns8nUZIokitKnI,1578
torchvision/transforms/v2/__pycache__/__init__.cpython-313.pyc,,
torchvision/transforms/v2/__pycache__/_augment.cpython-313.pyc,,
torchvision/transforms/v2/__pycache__/_auto_augment.cpython-313.pyc,,
torchvision/transforms/v2/__pycache__/_color.cpython-313.pyc,,
torchvision/transforms/v2/__pycache__/_container.cpython-313.pyc,,
torchvision/transforms/v2/__pycache__/_deprecated.cpython-313.pyc,,
torchvision/transforms/v2/__pycache__/_geometry.cpython-313.pyc,,
torchvision/transforms/v2/__pycache__/_meta.cpython-313.pyc,,
torchvision/transforms/v2/__pycache__/_misc.cpython-313.pyc,,
torchvision/transforms/v2/__pycache__/_temporal.cpython-313.pyc,,
torchvision/transforms/v2/__pycache__/_transform.cpython-313.pyc,,
torchvision/transforms/v2/__pycache__/_type_conversion.cpython-313.pyc,,
torchvision/transforms/v2/__pycache__/_utils.cpython-313.pyc,,
torchvision/transforms/v2/_augment.py,sha256=oYInTizGarT7k13cB6RNV9VjZcUi2fas_fGN2j339UU,16351
torchvision/transforms/v2/_auto_augment.py,sha256=7A0Bn79ESyrI5PCC9-lU8WtxweH1dqA-Ck7EzmFrQYY,32237
torchvision/transforms/v2/_color.py,sha256=-Q2RK-jhjVFxcc2jHM5TrGzv_gnXGIOrVInFkcVdxac,17001
torchvision/transforms/v2/_container.py,sha256=6wRnVyCyfWsoA6PQdsvDmOXMAK45HArA5FtkHuSM9Pw,6070
torchvision/transforms/v2/_deprecated.py,sha256=T4MVk-4eFNUc8ryeIJhQwxTM5O1mJLhseRY9ITWHsiY,1940
torchvision/transforms/v2/_geometry.py,sha256=VYXoehKR0MO0EEWYxKvvzp3X2gn9mmg7Uz9MWJGu3s8,67724
torchvision/transforms/v2/_meta.py,sha256=lUSxbAW9oZwDnb41Zc-orr-B5laoyOybjnW8BlvzhYE,3165
torchvision/transforms/v2/_misc.py,sha256=WtjZfNGX9ob2CXwtVWAsyyyrIKfc03XvekzEdDkdbqo,19111
torchvision/transforms/v2/_temporal.py,sha256=ZSmqtMebERMt4fX0RpTPzXMjpIXyLxRsMQx4FgYc2oE,899
torchvision/transforms/v2/_transform.py,sha256=YTIlDJOC8-Js1d4OVQimt4zNn83pZRltrpEX2lNZGcA,9316
torchvision/transforms/v2/_type_conversion.py,sha256=BztTkx80hnBn65j5kVVIgOEwbfMA2yZLe13utL1TiuM,2850
torchvision/transforms/v2/_utils.py,sha256=oE3ZCp6m1C3F1ldFYRcb9q1FB-KXkAKuPmHMdaKu-PM,8845
torchvision/transforms/v2/functional/__init__.py,sha256=5ae4N_r_7jzxEQ8CV6u1T4FH274m1aRF_mZyj3ksyWA,3861
torchvision/transforms/v2/functional/__pycache__/__init__.cpython-313.pyc,,
torchvision/transforms/v2/functional/__pycache__/_augment.cpython-313.pyc,,
torchvision/transforms/v2/functional/__pycache__/_color.cpython-313.pyc,,
torchvision/transforms/v2/functional/__pycache__/_deprecated.cpython-313.pyc,,
torchvision/transforms/v2/functional/__pycache__/_geometry.cpython-313.pyc,,
torchvision/transforms/v2/functional/__pycache__/_meta.cpython-313.pyc,,
torchvision/transforms/v2/functional/__pycache__/_misc.cpython-313.pyc,,
torchvision/transforms/v2/functional/__pycache__/_temporal.cpython-313.pyc,,
torchvision/transforms/v2/functional/__pycache__/_type_conversion.cpython-313.pyc,,
torchvision/transforms/v2/functional/__pycache__/_utils.cpython-313.pyc,,
torchvision/transforms/v2/functional/_augment.py,sha256=MRM8E3_gKfTTC0qFt3cKI4UxTxQtuGI9MeY2mBsrj04,3473
torchvision/transforms/v2/functional/_color.py,sha256=3t_d7YHsl5xPtkhLSiaTHFOKopQVU6d16e7DLNQUknQ,30377
torchvision/transforms/v2/functional/_deprecated.py,sha256=veBAiEXVKIdchR6x5a-b_-Kpmum-79tPAuP7kCLpl1U,795
torchvision/transforms/v2/functional/_geometry.py,sha256=s2XBk8W2bVTJtnpVoq8TtoUL7FT_Wca4MuHBsP22vCU,112532
torchvision/transforms/v2/functional/_meta.py,sha256=XLbVUJt5G8Jl23ja8_oO-Y36iFtOlBGNsUJlzvlEsbQ,28891
torchvision/transforms/v2/functional/_misc.py,sha256=_-24lHCYnJknqnRu5DzQKWq6vI08d2EC5bWRH9RavsI,18312
torchvision/transforms/v2/functional/_temporal.py,sha256=24CQCXXO12TnW7aUiUQdrk5DRSpTPONjjC4jaGh3lH4,1136
torchvision/transforms/v2/functional/_type_conversion.py,sha256=78wl0dNPwX08jOCW6KcZSGy8RAQqyxMtdrTUQVQlUTM,869
torchvision/transforms/v2/functional/_utils.py,sha256=6TDx4qExFfDO3-jBJisMhr28IdC55XyYMSbwUzge6jw,5488
torchvision/tv_tensors/__init__.py,sha256=ch1K7696ZxxJmXGpbEHRaPBKhJxxCDyGF3miSqEJs4M,1798
torchvision/tv_tensors/__pycache__/__init__.cpython-313.pyc,,
torchvision/tv_tensors/__pycache__/_bounding_boxes.cpython-313.pyc,,
torchvision/tv_tensors/__pycache__/_dataset_wrapper.cpython-313.pyc,,
torchvision/tv_tensors/__pycache__/_image.cpython-313.pyc,,
torchvision/tv_tensors/__pycache__/_keypoints.cpython-313.pyc,,
torchvision/tv_tensors/__pycache__/_mask.cpython-313.pyc,,
torchvision/tv_tensors/__pycache__/_torch_function_helpers.cpython-313.pyc,,
torchvision/tv_tensors/__pycache__/_tv_tensor.cpython-313.pyc,,
torchvision/tv_tensors/__pycache__/_video.cpython-313.pyc,,
torchvision/tv_tensors/_bounding_boxes.py,sha256=mZDgJNIvSb0pU4fRs6W8bX_lWei21_aBkYbbbBlqHPc,7732
torchvision/tv_tensors/_dataset_wrapper.py,sha256=fNnk3CSXipBNFsmnsPpa10DRN0I_Ly4Xib2Y5Zng9Ro,24505
torchvision/tv_tensors/_image.py,sha256=nh-p7Q9RZ3ESLb8gjM70ra0t6pXLg2HCvlA4VVUgvg8,1963
torchvision/tv_tensors/_keypoints.py,sha256=dulokMn4SYynXGZvsc395TtxI80UZWMxHiJYsHtXV0k,4584
torchvision/tv_tensors/_mask.py,sha256=k9kholaJt12YOvpyKfXnnilAzqgCt-4THrSCGAJ2GDU,1447
torchvision/tv_tensors/_torch_function_helpers.py,sha256=XvE6PI3PZLJvQFObVKCaZgyDZ1pVc5bjct27wi8thlM,2324
torchvision/tv_tensors/_tv_tensor.py,sha256=62cmwq1bmcs3ewzbaxfwJyqEj5mOiPqYNC6gBqYbTe8,6220
torchvision/tv_tensors/_video.py,sha256=hgbtLp9BSYHK4Mb5wqOhdPMDe5pd6gFPEF11YMh7p_k,1385
torchvision/utils.py,sha256=7DemrkDjNTZjUBr1J6MsdvrCgMOMVTwDrtWTy_XC9v0,33798
torchvision/version.py,sha256=GfqKDGQA0sAri-oYAiP233hO2acgcxS2vQ6h0s0nSzY,197
